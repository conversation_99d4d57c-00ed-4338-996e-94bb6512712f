"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Facebook, Instagram, Mail, MapPin, Phone, Twitter } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function ContactPage() {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    tourType: "",
    message: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (value: string) => {
    setFormData((prev) => ({ ...prev, tourType: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real application, you would send this data to your server
    console.log("Form submitted:", formData)

    toast({
      title: "Message Sent!",
      description: "We'll get back to you as soon as possible.",
    })

    // Reset form
    setFormData({
      name: "",
      email: "",
      phone: "",
      tourType: "",
      message: "",
    })
  }

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-[40vh] flex items-center">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wildlife/murchison-falls-dramatic.jpeg"
            alt="Dramatic view of Murchison Falls"
            fill
            className="object-cover brightness-50"
            priority
          />
        </div>
        <div className="container relative z-10 text-white">
          <div className="max-w-2xl space-y-4">
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl">Contact Us</h1>
            <p className="text-lg text-white/90">Get in touch with our team to plan your perfect Ugandan adventure.</p>
          </div>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-16">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-2xl font-bold mb-6">Send Us a Message</h2>
              <Card>
                <CardContent className="pt-6">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        name="name"
                        placeholder="Your full name"
                        required
                        value={formData.name}
                        onChange={handleChange}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          placeholder="Your email address"
                          required
                          value={formData.email}
                          onChange={handleChange}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                          id="phone"
                          name="phone"
                          placeholder="Your phone number"
                          value={formData.phone}
                          onChange={handleChange}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tourType">Interested In</Label>
                      <Select value={formData.tourType} onValueChange={handleSelectChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a tour package" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gorilla">Gorilla Trekking</SelectItem>
                          <SelectItem value="wildlife">Wildlife Safaris</SelectItem>
                          <SelectItem value="adventure">Adventure Escapades</SelectItem>
                          <SelectItem value="cultural">Cultural Tours</SelectItem>
                          <SelectItem value="birding">Bird Watching</SelectItem>
                          <SelectItem value="honeymoon">Honeymoon Packages</SelectItem>
                          <SelectItem value="custom">Custom Tour</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">Your Message</Label>
                      <Textarea
                        id="message"
                        name="message"
                        placeholder="Tell us about your travel plans, questions, or special requirements"
                        rows={5}
                        required
                        value={formData.message}
                        onChange={handleChange}
                      />
                    </div>

                    <Button type="submit" className="w-full">
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Contact Information */}
            <div>
              <h2 className="text-2xl font-bold mb-6">Contact Information</h2>

              <div className="space-y-8">
                <div className="bg-muted/50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Our Office</h3>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <MapPin className="h-5 w-5 text-primary mt-0.5" />
                      <div>
                        <p className="font-medium">Address</p>
                        <p className="text-muted-foreground">Kataza, Kampala, Uganda</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <Phone className="h-5 w-5 text-primary mt-0.5" />
                      <div>
                        <p className="font-medium">Phone</p>
                        <p className="text-muted-foreground">+256-788-359-512</p>
                        <p className="text-muted-foreground">+256-759-820-874 (WhatsApp)</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <Mail className="h-5 w-5 text-primary mt-0.5" />
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-muted-foreground"><EMAIL></p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-muted/50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Follow Us</h3>
                  <div className="flex gap-4">
                    <a
                      href="https://www.facebook.com/Jarthaztoursandtravel"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-primary/10 p-3 rounded-full hover:bg-primary/20 transition-colors"
                    >
                      <Facebook className="h-5 w-5 text-primary" />
                      <span className="sr-only">Facebook</span>
                    </a>
                    <a
                      href="https://x.com/JarthazTours"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-primary/10 p-3 rounded-full hover:bg-primary/20 transition-colors"
                    >
                      <Twitter className="h-5 w-5 text-primary" />
                      <span className="sr-only">Twitter</span>
                    </a>
                    <a
                      href="https://www.instagram.com/jarthaztoursandtravel/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-primary/10 p-3 rounded-full hover:bg-primary/20 transition-colors"
                    >
                      <Instagram className="h-5 w-5 text-primary" />
                      <span className="sr-only">Instagram</span>
                    </a>
                  </div>
                </div>

                <div className="h-[400px] relative bg-muted rounded-lg overflow-hidden">
                  <Image
                    src="/images/wildlife/murchison-falls-aerial.jpeg"
                    alt="Jarthaz Tours Location"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-black/25 flex items-end p-6">
                    <div className="bg-background/90 px-6 py-4 rounded-md backdrop-blur-sm">
                      <h3 className="font-semibold text-lg mb-2">Visit Us</h3>
                      <p className="text-muted-foreground text-sm">
                        Jarthaz Tours Uganda<br />
                        Plot 12, Main Street<br />
                        Kampala, Uganda
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-muted/50">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight mb-4">Frequently Asked Questions</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Find answers to common questions about traveling with Jarthaz Tours.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <Card>
              <CardContent className="pt-6">
                <h3 className="font-semibold text-lg mb-2">What is the best time to visit Uganda?</h3>
                <p className="text-muted-foreground">
                  Uganda can be visited year-round, but the dry seasons (December to February and June to August) are
                  generally considered the best times for wildlife viewing and gorilla trekking.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <h3 className="font-semibold text-lg mb-2">
                  How far in advance should I book a gorilla trekking permit?
                </h3>
                <p className="text-muted-foreground">
                  Gorilla permits are limited and in high demand. We recommend booking at least 6-12 months in advance,
                  especially if you plan to travel during peak season.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <h3 className="font-semibold text-lg mb-2">What vaccinations do I need for Uganda?</h3>
                <p className="text-muted-foreground">
                  Yellow fever vaccination is mandatory. Other recommended vaccinations include hepatitis A and B,
                  typhoid, and tetanus. Please consult with your healthcare provider before traveling.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <h3 className="font-semibold text-lg mb-2">Can you arrange custom tours?</h3>
                <p className="text-muted-foreground">
                  Yes! We specialize in creating personalized itineraries based on your interests, timeframe, and
                  budget. Contact us with your requirements, and we'll design a custom tour just for you.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-primary-foreground">
        <div className="container text-center">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Ready to Start Your Ugandan Adventure?</h2>
          <p className="mb-8 max-w-2xl mx-auto text-primary-foreground/90">
            Let's craft your perfect journey together. Our team is ready to assist you every step of the way.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" asChild>
              <a href="tel:+256788359512">Call Us Now</a>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="bg-transparent text-white border-white hover:bg-white hover:text-primary"
              asChild
            >
              <a href="mailto:<EMAIL>">Email Us</a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
